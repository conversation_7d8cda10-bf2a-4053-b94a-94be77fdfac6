import BPRLogo from '@/assets/logo';
import PrimaryButton from '@/components/common/button/primaryButton';
import { useAuthStore } from '@/globalProvider/authStore';
import { useAnalytics } from '@/globalProvider/analyticsProvider';
import { USER_LOGIN } from '@/utils/api';
import { ORGANIZATION_SESSION_KEY } from '@/constants/common';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState, useCallback } from 'react';
import login from '../assets/login.svg';
import BPRGearLogo from '@/assets/gearLogo';
import { analyticEvents } from '@/constants/analytic';

interface Organization {
  id: string;
  external_id: string;
  name: string;
  is_guest?: boolean;
}

interface FormElements extends HTMLFormControlsCollection {
  email: HTMLInputElement;
}
interface LoginFormElement extends HTMLFormElement {
  readonly elements: FormElements;
}

const SignIn: React.FC = () => {
  const [showNotification, setShowNotification] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string>('');
  const [loginStep, setLoginStep] = useState<1 | 2>(1);
  const [orgOptions, setOrgOptions] = useState<Organization[]>([]);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const { setOrg, orgInfo, user, accessToken, isLoading, setIsLoading } =
    useAuthStore();
  const analytics = useAnalytics();
  const router = useRouter();

  // Validate email format
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Auto-dismiss notification after 5 seconds
  useEffect(() => {
    if (showNotification && errorMessage) {
      const timer = setTimeout(() => setShowNotification(false), 5000);
      return () => clearTimeout(timer);
    }
  }, [showNotification, errorMessage]);

  // Redirect logic
  useEffect(() => {
    if (isRedirecting || isLoading) return;

    const organizationId = sessionStorage.getItem(ORGANIZATION_SESSION_KEY);
    if (
      accessToken &&
      user?.id &&
      organizationId &&
      router.pathname !== '/standard'
    ) {
      setIsRedirecting(true);
      router.push('/standard').finally(() => setIsRedirecting(false));
    }
    // If have accessToken but incomplete user data, stay on login page
    // Prevents redirect loops when auth is being cleared
    else if (
      accessToken &&
      (!user?.id || !organizationId) &&
      router.pathname === '/login'
    ) {
      // Stay on login page - auth is likely being refreshed/cleared
      return;
    }
    // If no accessToken and not on login page, redirect to login
    else if (!accessToken && router.pathname !== '/login') {
      setIsRedirecting(true);
      router.push('/login').finally(() => setIsRedirecting(false));
    }
  }, [accessToken, user, router.pathname, isLoading, isRedirecting]);

  // Handle login API call
  const handleLogin = useCallback(
    async (orgInfo: Organization, email: string) => {
      const params = {
        organization: orgInfo.external_id,
        connection: 'email',
        login_hint: email,
      };

      
      setOrg(orgInfo.external_id || email);
      sessionStorage.setItem(ORGANIZATION_SESSION_KEY, orgInfo.id);
      analytics.trackEvent(analyticEvents.LOGIN_STARTED, {...params, ...orgInfo})

      try {
        const queryParams = new URLSearchParams(params).toString();
        // Always go through proper auth flow when user is explicitly logging in
        // Don't rely on existing tokens as they might be invalid/incomplete
        setIsRedirecting(true);
        window.location.href = `/api/auth/login?${queryParams}`;
      } catch (error) {
        console.error('Login failed:', error);
        setErrorMessage(
          'Failed to log in. Please try again or contact support.',
        );
        setShowNotification(true);
        sessionStorage.removeItem(ORGANIZATION_SESSION_KEY);
        setIsRedirecting(false);
      } finally {
        if (!isRedirecting) setIsLoading(false);
      }
    },
    [accessToken, router, setOrg, setIsLoading],
  );

  // Handle organization selection
  const handleOrganizationList = useCallback(
    async (option: Organization) => {
      if (!userEmail) return;

      setIsLoading(true);
      setIsRedirecting(true);
      try {
        await handleLogin(option, userEmail);
      } catch (error) {
        console.error('Organization selection failed:', error);
        setErrorMessage('Failed to select organization. Please try again.');
        setShowNotification(true);
        setIsRedirecting(false);
      } finally {
        if (!isRedirecting) setIsLoading(false);
      }
    },
    [userEmail, handleLogin],
  );

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent<LoginFormElement>) => {
    event.preventDefault();
    setIsLoading(true);
    setErrorMessage(null);

    const email = event.currentTarget.elements.email.value;
    if (!email || !validateEmail(email)) {
      setErrorMessage('Please enter a valid email address.');
      setShowNotification(true);
      setIsLoading(false);
      return;
    }

    setUserEmail(email);
    try {
      const headers = {
        'Content-Type': 'application/json',
        'api-key': btoa(email), // TODO: SECURITY RISK - Replace with secure authentication method
        // This is a temporary solution and should be replaced with proper API authentication
      };

      const res = await fetch(USER_LOGIN, { method: 'POST', headers });
      if (res.status === 200) {
        const { record: orgInfo } = await res.json();
        const organizations = Array.isArray(orgInfo) ? orgInfo : [orgInfo];

        if (organizations.length > 1) {
          setOrgOptions(organizations);
          setLoginStep(2);
          setIsLoading(false);
        } else {
          await handleLogin(organizations[0], email);
        }
      } else {
        const errorData = await res.json().catch(() => ({}));
        setErrorMessage(
          errorData.error || 'Failed to authenticate. Please try again.',
        );
        setShowNotification(true);
      }
    } catch (error) {
      console.error('Submit failed:', error);
      setErrorMessage('An unexpected error occurred. Please try again.');
      setShowNotification(true);
    } finally {
      if (!isRedirecting) setIsLoading(false);
    }
  };

  if (isLoading || isRedirecting) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-400" />
          <p className="mt-4 text-primary-400">
            {isRedirecting ? 'Redirecting...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-wrap items-center min-h-screen">
      <div className="w-full xl:w-1/2 p-4 sm:p-8 xl:p-6">
        {loginStep === 1 && (
          <div className="mx-5 sm:mx-20">
            <div className="flex flex-col justify-center">
              <div
                className="mb-2 bg-primary-100 rounded-lg flex items-center justify-center"
                style={{ width: '60px', height: '60px' }}
              >
                <BPRGearLogo width={'36'} height={'36'} aria-label="BPR Logo" />
              </div>
              <h2 className="mb-8 mt-4 text-3xl font-bold text-black dark:text-white">
                Welcome back!
              </h2>
            </div>

            {showNotification && errorMessage && (
              <div
                role="alert"
                className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg"
              >
                {errorMessage}
              </div>
            )}

            <form onSubmit={handleSubmit} noValidate>
              <div className="mb-6">
                <label
                  htmlFor="email"
                  className="mb-2.5 block font-medium text-black dark:text-white"
                >
                  Email
                </label>
                <div className="relative">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    placeholder="Enter your work email here"
                    className="w-full rounded-lg border border-black border-opacity-30 bg-transparent py-3 pl-4 pr-10 text-black outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                  />
                  <span className="absolute right-4 top-3.5">
                    <svg
                      className="fill-current"
                      width="22"
                      height="22"
                      viewBox="0 0 22 22"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      aria-hidden="true"
                    >
                      <path
                        d="M19.2516 3.30005H2.75156C1.58281 3.30005 0.585938 4.26255 0.585938 5.46567V16.6032C0.585938 17.7719 1.54844 18.7688 2.75156 18.7688H19.2516C20.4203 18.7688 21.4172 17.8063 21.4172 16.6032V5.4313C21.4172 4.26255 20.4203 3.30005 19.2516 3.30005ZM19.2516 4.84692C19.2859 4.84692 19.3203 4.84692 19.3547 4.84692L11.0016 10.2094L2.64844 4.84692C2.68281 4.84692 2.71719 4.84692 2.75156 4.84692H19.2516ZM19.2516 17.1532H2.75156C2.40781 17.1532 2.13281 16.8782 2.13281 16.5344V6.35942L10.1766 11.5157C10.4172 11.6875 10.6922 11.7563 10.9672 11.7563C11.2422 11.7563 11.5172 11.6875 11.7578 11.5157L19.8016 6.35942V16.5688C19.8703 16.9125 19.5953 17.1532 19.2516 17.1532Z"
                        fill=""
                      />
                    </svg>
                  </span>
                </div>
              </div>
              <PrimaryButton
                type="submit"
                text="Continue"
                buttonClasses="!px-5 !py-2"
                isLoading={isLoading}
                width="100%"
                disabled={isLoading}
              />
            </form>
            <div className="mt-8 text-sm text-gray-600 dark:text-gray-300">
              By signing in, you agree to our{' '}
              <Link
                href="https://www.bprhub.com/terms-of-use"
                className="font-medium text-primary-400"
              >
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link
                href="https://www.bprhub.com/privacy"
                className="font-medium text-primary-400"
              >
                Privacy Policy
              </Link>
            </div>
          </div>
        )}
        {loginStep === 2 && orgOptions.length > 0 && (
          <div className="mx-5 sm:mx-20 p-8 shadow-default rounded-md">
            <h2 className="mb-6 text-2xl font-bold text-black dark:text-white">
              Select Organization
            </h2>
            <ul role="listbox" aria-label="Select an organization">
              {orgOptions.map((item) => (
                <li
                  key={item.external_id}
                  role="option"
                  aria-selected={false}
                  onClick={() => !isLoading && handleOrganizationList(item)}
                  className={`flex items-center p-3 border rounded-lg mb-3 cursor-pointer ${
                    isLoading
                      ? 'opacity-50 pointer-events-none'
                      : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <span>{item.name}</span>
                  <button
                    className="ml-auto h-10 w-10 flex items-center justify-center"
                    disabled={isLoading}
                    aria-label={`Select ${item.name}`}
                  >
                    <svg
                      className="w-6 h-6 text-gray-800 dark:text-white"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke="currentColor"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M16 12H4m12 0-4 4m4-4-4-4m3-4h2a3 3 0 0 1 3 3v10a3 3 0 0 1-3 3h-2"
                      />
                    </svg>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
      <div className="w-full hidden xl:block xl:w-1/2 h-screen">
        <Image
          src={login}
          alt="Login illustration"
          className="h-screen object-cover w-full"
          priority
          sizes="50vw"
        />
      </div>
    </div>
  );
};

export default SignIn;
