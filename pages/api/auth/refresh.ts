// import { getSession, updateSession } from '@auth0/nextjs-auth0';
import { NextApiRequest, NextApiResponse } from 'next';
import auth0 from './auth0';
import axios from 'axios';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  try {
    // Add request method validation
    if (req.method !== 'GET' && req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const session = await auth0.getSession(req, res);

    if (!session) {
      console.log('No session found during refresh attempt');
      return res.status(401).json({ error: 'Not authenticated' });
    }

    const refreshToken = session.refreshToken;
    const organization = session.user?.org_id;
    const userId = session.user?.sub;
    const userEmail = session.user?.email;

    // Additional security checks
    if (!refreshToken) {
      console.error('No refresh token available for user:', userId);
      return res.status(401).json({ error: 'No refresh token available' });
    }

    if (!userId) {
      console.error('Invalid session - no user ID found');
      return res.status(401).json({ error: 'Invalid session - no user ID' });
    }

    // Log refresh attempt with more context for security monitoring
    console.log(
      'Refreshing token for user:',
      userId,
      'email:',
      userEmail,
      'org:',
      organization,
    );

    // Add rate limiting check (basic implementation)
    const userAgent = req.headers['user-agent'] || 'unknown';
    const clientIP =
      req.headers['x-forwarded-for'] ||
      req.connection.remoteAddress ||
      'unknown';
    console.log('Refresh request from IP:', clientIP, 'User-Agent:', userAgent);

    // Call Auth0 to refresh the token with `organization`
    const response = await axios.post(
      `${process.env.AUTH0_ISSUER_BASE_URL}/oauth/token`,
      {
        grant_type: 'refresh_token',
        client_id: process.env.AUTH0_CLIENT_ID,
        client_secret: process.env.AUTH0_CLIENT_SECRET,
        refresh_token: refreshToken,
        organization, // 👈 Ensure the organization is sent
      },
      { headers: { 'Content-Type': 'application/json' } },
    );

    const newAccessToken = response.data.access_token;
    const newRefreshToken = response.data.refresh_token || refreshToken;

    // Verify the new access token belongs to the same user
    // Decode the JWT to check the subject (user ID)
    let tokenPayload;
    try {
      const tokenParts = newAccessToken.split('.');
      if (tokenParts.length !== 3) {
        throw new Error('Invalid JWT format');
      }
      tokenPayload = JSON.parse(
        Buffer.from(tokenParts[1], 'base64').toString(),
      );
    } catch (jwtError) {
      console.error('Failed to decode JWT token:', jwtError);
      return res.status(500).json({ error: 'Invalid access token format' });
    }

    if (tokenPayload.sub !== userId) {
      console.error('Token refresh security violation: User ID mismatch', {
        sessionUserId: userId,
        tokenUserId: tokenPayload.sub,
        sessionEmail: userEmail,
        tokenEmail: tokenPayload.email,
        clientIP: clientIP,
        userAgent: userAgent,
        timestamp: new Date().toISOString(),
      });
      return res
        .status(403)
        .json({ error: 'Token refresh failed - security violation' });
    }

    // Additional validation: check if email matches (if available)
    if (userEmail && tokenPayload.email && tokenPayload.email !== userEmail) {
      console.error('Token refresh security violation: Email mismatch', {
        sessionEmail: userEmail,
        tokenEmail: tokenPayload.email,
        userId: userId,
        clientIP: clientIP,
        timestamp: new Date().toISOString(),
      });
      return res
        .status(403)
        .json({ error: 'Token refresh failed - email security violation' });
    }

    // Validate organization context if present
    if (
      organization &&
      tokenPayload.org_id &&
      tokenPayload.org_id !== organization
    ) {
      console.error('Token refresh security violation: Organization mismatch', {
        sessionOrg: organization,
        tokenOrg: tokenPayload.org_id,
        userId: userId,
        clientIP: clientIP,
        timestamp: new Date().toISOString(),
      });
      return res
        .status(403)
        .json({
          error: 'Token refresh failed - organization security violation',
        });
    }

    // Create a new session object to prevent session pollution
    const updatedSession = {
      user: {
        ...session.user,
        // Ensure user data is consistent with the new token
        sub: tokenPayload.sub,
        email: tokenPayload.email || session.user?.email,
        org_id: tokenPayload.org_id || session.user?.org_id,
      },
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
      accessTokenExpiresAt: response.data.expires_in
        ? Date.now() + response.data.expires_in * 1000
        : undefined,
    };

    await auth0.updateSession(req, res, updatedSession);

    console.log(
      'Token refresh successful for user:',
      userId,
      'email:',
      userEmail,
    );

    res.json({
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
      expiresIn: response.data.expires_in,
    });
  } catch (error) {
    console.error('Error refreshing token:', error);

    // Log security issues for audit purposes
    if (
      axios.isAxiosError(error) &&
      (error.response?.status === 400 || error.response?.status === 401)
    ) {
      console.error(
        'Authentication error during token refresh - client should handle logout:',
        {
          status: error.response?.status,
          data: error.response?.data,
        },
      );
      return res.status(401).json({
        error: 'Authentication failed',
        shouldLogout: true,
      });
    }

    res.status(500).json({ error: 'Failed to refresh access token' });
  }
}
