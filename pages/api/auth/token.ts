import { NextApiRequest, NextApiResponse } from 'next';
import auth0 from './auth0';

// TypeScript type for the response payload
interface TokenResponse {
  accessToken: string;
}

const handler = auth0.withApiAuthRequired(
  async (
    req: NextApiRequest,
    res: NextApiResponse<TokenResponse | { message: string }>,
  ) => {
    // Remove sensitive environment variable logging in production
    if (process.env.NODE_ENV !== 'production') {
      console.log(
        'AUTH0_SECRET_token = ',
        process.env.AUTH0_SECRET?.substring(0, 10) + '...',
      );
    }

    try {
      // Add method validation
      if (req.method !== 'GET' && req.method !== 'POST') {
        return res.status(405).json({ message: 'Method not allowed' });
      }

      const session = await auth0.getSession(req, res);
      if (!session) {
        return res.status(401).json({ message: 'No valid session found' });
      }

      const { accessToken } = await auth0.getAccessToken(req, res);

      if (!accessToken) {
        console.error('Access token not found for user:', session.user?.sub);
        return res.status(500).json({ message: 'Access token not found' });
      }

      // Log token access for security monitoring
      console.log(
        'Access token requested by user:',
        session.user?.sub,
        'email:',
        session.user?.email,
      );

      res.status(200).json({ accessToken });
    } catch (error) {
      console.error('Error fetching access token:', error);
      res.status(500).json({ message: 'Failed to fetch access token' });
    }
  },
);

export default handler;
